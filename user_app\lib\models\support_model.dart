import 'package:cloud_firestore/cloud_firestore.dart';

enum SupportStatus {
  pending,
  inProgress,
  resolved,
  closed,
}

extension SupportStatusExtension on SupportStatus {
  String get displayName {
    switch (this) {
      case SupportStatus.pending:
        return 'Pending';
      case SupportStatus.inProgress:
        return 'In Progress';
      case SupportStatus.resolved:
        return 'Resolved';
      case SupportStatus.closed:
        return 'Closed';
    }
  }

  String get value {
    return name;
  }
}

class SupportModel {
  final String id;
  final String? referenceId;
  final String contactNumber;
  final String address;
  final String category;
  final String message;
  final SupportStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? adminNote;
  final String? assignedAdminId;
  final String? assignedAdminName;

  SupportModel({
    required this.id,
    this.referenceId,
    required this.contactNumber,
    required this.address,
    required this.category,
    required this.message,
    this.status = SupportStatus.pending,
    required this.createdAt,
    required this.updatedAt,
    this.adminNote,
    this.assignedAdminId,
    this.assignedAdminName,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'referenceId': referenceId,
      'contactNumber': contactNumber,
      'address': address,
      'category': category,
      'message': message,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'adminNote': adminNote,
      'assignedAdminId': assignedAdminId,
      'assignedAdminName': assignedAdminName,
    };
  }

  // Create SupportModel from Firestore document
  factory SupportModel.fromMap(Map<String, dynamic> map) {
    return SupportModel(
      id: map['id'] ?? '',
      referenceId: map['referenceId'],
      contactNumber: map['contactNumber'] ?? '',
      address: map['address'] ?? '',
      category: map['category'] ?? 'General',
      message: map['message'] ?? '',
      status: SupportStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => SupportStatus.pending,
      ),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      adminNote: map['adminNote'],
      assignedAdminId: map['assignedAdminId'],
      assignedAdminName: map['assignedAdminName'],
    );
  }

  // Create SupportModel from Firestore document
  factory SupportModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SupportModel.fromMap(data);
  }

  // Copy with method for updates
  SupportModel copyWith({
    String? id,
    String? referenceId,
    String? contactNumber,
    String? address,
    String? category,
    String? message,
    SupportStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? adminNote,
    String? assignedAdminId,
    String? assignedAdminName,
  }) {
    return SupportModel(
      id: id ?? this.id,
      referenceId: referenceId ?? this.referenceId,
      contactNumber: contactNumber ?? this.contactNumber,
      address: address ?? this.address,
      category: category ?? this.category,
      message: message ?? this.message,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      adminNote: adminNote ?? this.adminNote,
      assignedAdminId: assignedAdminId ?? this.assignedAdminId,
      assignedAdminName: assignedAdminName ?? this.assignedAdminName,
    );
  }
}
