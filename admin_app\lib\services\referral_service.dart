import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';
import '../models/referral_model.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';

class ReferralService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'referrals';
  static const String _settingsCollection = 'referral_settings';

  /// Generate unique referral code
  static String generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return List.generate(8, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Complete a referral
  static Future<bool> completeReferral(String referralId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(referralId)
          .update({
        'status': ReferralStatus.completed.value,
        'completedAt': Timestamp.fromDate(DateTime.now()),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error completing referral: $e');
      return false;
    }
  }

  /// Cancel a referral
  static Future<bool> cancelReferral(String referralId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(referralId)
          .update({
        'status': ReferralStatus.cancelled.value,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error cancelling referral: $e');
      return false;
    }
  }

  /// Get referral settings
  static Future<Map<String, dynamic>> getReferralSettings() async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc('default')
          .get();

      if (doc.exists) {
        return doc.data() ?? {};
      } else {
        // Return default settings
        return {
          'rewardAmount': 50.0,
          'expiryDays': 30,
          'isEnabled': true,
          'minimumPurchaseAmount': 100.0,
        };
      }
    } catch (e) {
      print('Error getting referral settings: $e');
      return {
        'rewardAmount': 50.0,
        'expiryDays': 30,
        'isEnabled': true,
        'minimumPurchaseAmount': 100.0,
      };
    }
  }

  /// Update referral settings (admin only)
  static Future<bool> updateReferralSettings(Map<String, dynamic> settings) async {
    try {
      await _firestore
          .collection(_settingsCollection)
          .doc('default')
          .set({
        ...settings,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      }, SetOptions(merge: true));

      return true;
    } catch (e) {
      print('Error updating referral settings: $e');
      return false;
    }
  }

  /// Get all referrals (admin only)
  static Future<List<ReferralModel>> getAllReferrals({
    ReferralStatus? statusFilter,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      if (statusFilter != null) {
        query = query.where('status', isEqualTo: statusFilter.value);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => ReferralModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting all referrals: $e');
      return [];
    }
  }

  /// Get referral statistics (admin only)
  static Future<Map<String, dynamic>> getReferralStatistics() async {
    try {
      final allReferrals = await _firestore.collection(_collection).get();
      
      final total = allReferrals.docs.length;
      final completed = allReferrals.docs.where((doc) =>
          doc.data()['status'] == ReferralStatus.completed.value).length;
      final expired = allReferrals.docs.where((doc) =>
          doc.data()['status'] == ReferralStatus.expired.value).length;
      
      double totalRewards = 0.0;
      for (final doc in allReferrals.docs) {
        final data = doc.data();
        if (data['status'] == ReferralStatus.completed.value) {
          totalRewards += (data['rewardAmount'] ?? 0.0).toDouble();
        }
      }

      return {
        'totalReferrals': total,
        'completedReferrals': completed,
        'expiredReferrals': expired,
        'totalRewards': totalRewards,
        'conversionRate': total > 0 ? (completed / total * 100) : 0.0,
      };
    } catch (e) {
      print('Error getting referral statistics: $e');
      return {
        'totalReferrals': 0,
        'completedReferrals': 0,
        'expiredReferrals': 0,
        'totalRewards': 0.0,
        'conversionRate': 0.0,
      };
    }
  }

  /// Get referrer information for a user (by referee ID)
  static Future<ReferralModel?> getReferralByReferee(String refereeId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('refereeId', isEqualTo: refereeId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return ReferralModel.fromDocument(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Error getting referral by referee: $e');
      return null;
    }
  }

  /// Get referrer user information for a user
  static Future<UserModel?> getReferrerUser(String refereeId) async {
    try {
      final referral = await getReferralByReferee(refereeId);
      if (referral != null) {
        final userDoc = await _firestore
            .collection('users')
            .doc(referral.referrerId)
            .get();

        if (userDoc.exists) {
          return UserModel.fromDocument(userDoc);
        }
      }
      return null;
    } catch (e) {
      print('Error getting referrer user: $e');
      return null;
    }
  }

  /// Expire old referrals (admin maintenance) - No longer needed since referrals auto-complete
  static Future<void> expireOldReferrals() async {
    // No longer needed since referrals are auto-completed
    print('Referrals are now auto-completed, no expiration needed');
  }

  /// Get top referrers (admin analytics)
  static Future<List<Map<String, dynamic>>> getTopReferrers({int limit = 10}) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReferralStatus.completed.value)
          .get();

      // Group by referrer
      final Map<String, Map<String, dynamic>> referrerStats = {};
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final referrerId = data['referrerId'];
        final referrerName = data['referrerName'];
        final rewardAmount = (data['rewardAmount'] ?? 0.0).toDouble();
        
        if (referrerStats.containsKey(referrerId)) {
          referrerStats[referrerId]!['count'] += 1;
          referrerStats[referrerId]!['totalEarnings'] += rewardAmount;
        } else {
          referrerStats[referrerId] = {
            'referrerId': referrerId,
            'referrerName': referrerName,
            'count': 1,
            'totalEarnings': rewardAmount,
          };
        }
      }

      // Sort by count and take top
      final sortedReferrers = referrerStats.values.toList()
        ..sort((a, b) => b['count'].compareTo(a['count']));

      return sortedReferrers.take(limit).toList();
    } catch (e) {
      print('Error getting top referrers: $e');
      return [];
    }
  }

  /// Search referrals by code or user info
  static Future<List<ReferralModel>> searchReferrals(String query) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(100) // Get more to filter
          .get();

      final allReferrals = querySnapshot.docs
          .map((doc) => ReferralModel.fromDocument(doc))
          .toList();

      // Filter by search query
      final searchLower = query.toLowerCase();
      final filteredReferrals = allReferrals.where((referral) {
        return referral.referralCode.toLowerCase().contains(searchLower) ||
               referral.referrerName.toLowerCase().contains(searchLower) ||
               referral.referrerEmail.toLowerCase().contains(searchLower) ||
               referral.refereeName.toLowerCase().contains(searchLower) ||
               referral.refereeEmail.toLowerCase().contains(searchLower);
      }).toList();

      return filteredReferrals;
    } catch (e) {
      print('Error searching referrals: $e');
      return [];
    }
  }
}
