import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../services/post_service.dart';
import '../services/product_service.dart';
import '../services/user_service.dart';
import '../services/chat_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/post_card.dart';
import '../widgets/role_indicator.dart';
import '../widgets/common/country_flag.dart';
import '../utils/role_access_control.dart';
import 'product_detail_screen.dart';
import 'chat_screen.dart';

import '../services/post_service.dart';
import '../services/product_service.dart';
import '../models/post_model.dart';
import '../widgets/profile/referral_info_card.dart';
import '../models/product_model.dart';

class UserProfileScreen extends StatefulWidget {
  final String userId;
  final String? initialUserName; // Optional: for display while loading

  const UserProfileScreen({
    super.key,
    required this.userId,
    this.initialUserName,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  UserModel? _user;
  bool _isLoading = true;
  String? _error;
  bool _isFollowing = false;
  List<PostModel>? _userPosts;
  List<ProductModel>? _userProducts;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final user = await UserService.getUserById(widget.userId);

      // Load user posts and products
      final posts = await PostService.getPostsByUserId(widget.userId);
      final products = await ProductService.getProductsBySellerId(widget.userId);

      // Check if current user is following this user
      bool isFollowing = false;
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null && user != null) {
        isFollowing = user.isFollowedBy(currentUser.id);
      }

      setState(() {
        _user = user;
        _userPosts = posts;
        _userProducts = products;
        _isFollowing = isFollowing;
        _isLoading = false;
      });

      // Initialize TabController based on user role after setState
      if (user != null && mounted) {
        _tabController?.dispose();
        // Always show Posts tab, and Products tab if user can access it
        int tabCount = RoleAccessControl.canAccessProductDashboard(user) ? 2 : 1;
        _tabController = TabController(length: tabCount, vsync: this);
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          _user?.displayName ?? widget.initialUserName ?? 'Profile',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'copy_link',
                child: Row(
                  children: [
                    Icon(Icons.link, size: 20),
                    SizedBox(width: 12),
                    Text('Copy Link'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'block',
                child: Row(
                  children: [
                    Icon(Icons.block, color: AppConstants.errorColor, size: 20),
                    SizedBox(width: 12),
                    Text(
                      'Block User',
                      style: TextStyle(color: AppConstants.errorColor),
                    ),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, color: AppConstants.errorColor, size: 20),
                    SizedBox(width: 12),
                    Text(
                      'Report User',
                      style: TextStyle(color: AppConstants.errorColor),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: AppConstants.primaryColor,
              ),
            )
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load profile',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadUserProfile,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _user == null
                  ? const Center(child: Text('User not found'))
                  : RefreshIndicator(
                      onRefresh: _loadUserProfile,
                      color: AppConstants.primaryColor,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildUserProfileContainer(_user!),

                            // Profile Tabs Section
                            Padding(
                              padding: const EdgeInsets.only(top: 16, bottom: 100),
                              child: _buildProfileTabs(),
                            ),
                          ],
                        ),
                      ),
                    ),
    );
  }

  Widget _buildUserProfileContainer(UserModel user) {
    return Column(
      children: [
        // Profile Header with Cover, Profile Photo and User Info (Same as ProfileHeader)
        _buildProfileHeader(user),

        const SizedBox(height: 16),

        // Statistics Container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildStatisticsCard(user),
          ),
        ),

        const SizedBox(height: 16),

        // Referral Section Container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ReferralInfoCard(user: user),
          ),
        ),

        const SizedBox(height: 16),

        // Action Buttons Container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildUserActionButtons(),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileHeader(UserModel user) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Cover Photo Section with Profile Picture Overlay
          _buildCoverWithProfileSection(user),

          // User Info Section
          _buildUserInfoSection(user),
        ],
      ),
    );
  }

  Widget _buildCoverWithProfileSection(UserModel user) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Cover Photo
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: user.coverImageUrl != null
                  ? [
                      Colors.black.withOpacity(0.3),
                      Colors.transparent,
                    ]
                  : [
                      AppConstants.primaryColor,
                      AppConstants.primaryColor.withOpacity(0.8),
                      AppConstants.secondaryColor.withOpacity(0.6),
                    ],
            ),
            image: user.coverImageUrl != null
                ? DecorationImage(
                    image: CachedNetworkImageProvider(user.coverImageUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: Stack(
            children: [
              // Decorative Elements
              _buildDecorativeElements(),
            ],
          ),
        ),

        // Profile Picture positioned at bottom of cover
        Positioned(
          bottom: -50,
          left: 20,
          child: _buildProfilePicture(user),
        ),
      ],
    );
  }

  Widget _buildUserInfoSection(UserModel user) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display Name with Role and Verification Badge
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          Flexible(
                            child: Text(
                              user.displayName,
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppConstants.textPrimaryColor,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          RoleIndicator(
                            user: user,
                            size: 20,
                          ),
                          if (user.country != null) ...[
                            const SizedBox(width: 8),
                            CountryFlag(
                              countryCode: CountryHelper.getCountryCode(user.country),
                              size: 20,
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (user.isVerified == true) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.verified,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 8),

          // Username
          Text(
            '@${user.username}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 12),

          // Bio (if available)
          if (user.bio != null && user.bio!.isNotEmpty) ...[
            Text(
              user.bio!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textPrimaryColor,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Location and Joining Date
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Location row
              if (user.address != null || user.country != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: AppConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        _getLocationText(user),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],
              // Joining date row
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 16,
                    color: AppConstants.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Joined ${_formatJoiningDate(user.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDecorativeElements() {
    return Stack(
      children: [
        Positioned(
          top: 40,
          right: 60,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.1),
            ),
          ),
        ),
        Positioned(
          top: 80,
          left: 40,
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.05),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfilePicture(UserModel user) {
    return Stack(
      children: [
        // Main profile picture
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _viewProfilePhotoFullscreen(user.profileImageUrl);
          },
          child: Hero(
            tag: 'profile_photo_${user.id}',
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipOval(
                child: _buildProfileImage(user),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImage(UserModel user) {
    if (user.profileImageUrl != null) {
      return CachedNetworkImage(
        imageUrl: user.profileImageUrl!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppConstants.primaryColor.withOpacity(0.3),
                AppConstants.secondaryColor.withOpacity(0.3),
              ],
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        errorWidget: (context, url, error) => _buildDefaultAvatar(user),
      );
    }
    return _buildDefaultAvatar(user);
  }

  Widget _buildDefaultAvatar(UserModel user) {
    return Center(
      child: Text(
        user.displayName.isNotEmpty
            ? user.displayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          fontSize: 50,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildInfoRow(UserModel user) {
    return Column(
      children: [
        // Location row
        if (user.address != null || user.country != null) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 16,
                color: AppConstants.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  _getLocationText(user),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        // Joining date row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 16,
              color: AppConstants.textSecondaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              'Joined ${_formatJoiningDate(user.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getLocationText(UserModel user) {
    final locationParts = <String>[];

    if (user.address != null && user.address!.isNotEmpty) {
      locationParts.add(user.address!);
    }

    if (user.country != null && user.country!.isNotEmpty) {
      locationParts.add(user.country!);
    }

    return locationParts.join(', ');
  }

  Widget _buildStatisticsCard(UserModel user) {
    final postsCount = (_userPosts?.length ?? 0) + (_userProducts?.length ?? 0);

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            '${user.followerCount}',
            'Followers',
          ),
        ),
        const SizedBox(width: 1),
        Expanded(
          child: _buildStatCard(
            context,
            '${user.followingCount}',
            'Following',
          ),
        ),
        const SizedBox(width: 1),
        Expanded(
          child: _buildStatCard(
            context,
            '$postsCount',
            'Posts',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String count,
    String label,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            count,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildUserActionButtons() {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final isOwnProfile = currentUser?.id == widget.userId;

    if (isOwnProfile) {
      return const SizedBox.shrink(); // Don't show action buttons for own profile
    }

    return Column(
      children: [
        // Follow Button
        SizedBox(
          width: double.infinity,
          height: 40,
          child: ElevatedButton(
            onPressed: () => _toggleFollow(),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isFollowing
                  ? AppConstants.backgroundColor
                  : AppConstants.primaryColor,
              foregroundColor: _isFollowing
                  ? AppConstants.textPrimaryColor
                  : AppConstants.onPrimaryColor,
              side: _isFollowing
                  ? BorderSide(color: AppConstants.primaryColor, width: 1)
                  : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              _isFollowing ? 'Following' : 'Follow',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Chat Now Button
        SizedBox(
          width: double.infinity,
          height: 40,
          child: OutlinedButton.icon(
            onPressed: () => _startChat(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppConstants.primaryColor, width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: Icon(
              Icons.chat_bubble_outline,
              size: 18,
              color: AppConstants.primaryColor,
            ),
            label: Text(
              'Chat Now',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _viewProfilePhotoFullscreen(String? imageUrl) {
    if (imageUrl == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: () => _shareProfilePhoto(),
                ),
              ),
            ],
          ),
          body: Center(
            child: Hero(
              tag: 'profile_photo_fullscreen',
              child: InteractiveViewer(
                minScale: 0.5,
                maxScale: 3.0,
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.contain,
                  placeholder: (context, url) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppConstants.primaryColor.withOpacity(0.3),
                          AppConstants.secondaryColor.withOpacity(0.3),
                        ],
                      ),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppConstants.primaryColor.withOpacity(0.3),
                          AppConstants.secondaryColor.withOpacity(0.3),
                        ],
                      ),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 50,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Failed to load image',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _shareProfilePhoto() async {
    if (_user?.profileImageUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No profile photo to share'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    try {
      final String shareText = '''
👤 Check out ${_user!.displayName}'s profile on Amal Point!

${_user!.bio ?? 'Join Amal Point to connect with amazing people!'}

Download Amal Point app now!
''';

      await Share.share(
        shareText,
        subject: '${_user!.displayName} on Amal Point',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share profile'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  Widget _buildProfileTabs() {
    if (_user == null || _tabController == null) return const SizedBox.shrink();

    // Get tabs based on user role
    List<Tab> tabs = [];
    List<Widget> tabViews = [];

    // Always add Posts tab first
    tabs.add(const Tab(
      icon: Icon(Icons.grid_on),
      text: 'Posts',
    ));
    tabViews.add(_buildPostsGrid());

    // Add Products tab only if user can access product dashboard
    if (RoleAccessControl.canAccessProductDashboard(_user)) {
      tabs.add(const Tab(
        icon: Icon(Icons.shopping_bag_outlined),
        text: 'Products',
      ));
      tabViews.add(_buildProductsGrid());
    }

    return Container(
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Tab Header
          Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController!,
              labelColor: AppConstants.primaryColor,
              unselectedLabelColor: AppConstants.textSecondaryColor,
              indicatorColor: AppConstants.primaryColor,
              indicatorWeight: 3,
              tabs: tabs,
            ),
          ),
          // Tab Content
          SizedBox(
            height: 400, // Fixed height for tab content
            child: TabBarView(
              controller: _tabController!,
              children: tabViews,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsGrid() {
    if (_user == null) return const SizedBox.shrink();

    return FutureBuilder<List<ProductModel>>(
      future: ProductService.getProductsBySellerId(_user!.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Error loading products: ${snapshot.error}'),
          );
        }

        final products = snapshot.data ?? [];

        if (products.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_bag_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No products yet',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(
                AppConstants.paddingMedium,
                AppConstants.paddingMedium,
                AppConstants.paddingMedium,
                120, // Increased bottom padding for navigation gap
              ),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 6, // Reduced spacing for more card space
                  mainAxisSpacing: 6, // Reduced spacing for more card space
                  childAspectRatio: 0.78, // Balanced ratio - bigger than before but not too tall
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final product = products[index];
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ProductDetailScreen(product: product),
                          ),
                        );
                      },
                      child: Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(AppConstants.borderRadiusMedium),
                                    ),
                                    child: product.imageUrls.isNotEmpty
                                        ? CachedNetworkImage(
                                            imageUrl: product.imageUrls.first,
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            height: double.infinity,
                                            placeholder: (context, url) => Container(
                                              color: Colors.grey.shade200,
                                              child: const Center(child: CircularProgressIndicator()),
                                            ),
                                            errorWidget: (context, url, error) => Container(
                                              color: Colors.grey.shade200,
                                              child: const Icon(Icons.error),
                                            ),
                                          )
                                        : Container(
                                            color: Colors.grey.shade200,
                                            child: const Icon(Icons.image, size: 50),
                                          ),
                                  ),
                                  // Category overlay
                                  Positioned(
                                    bottom: 8,
                                    left: 8,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.7),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        product.category,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    product.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '৳${product.price}',
                                    style: TextStyle(
                                      color: AppConstants.primaryColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  childCount: products.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPostsGrid() {
    if (_user == null) return const SizedBox.shrink();

    return FutureBuilder<List<PostModel>>(
      future: PostService.getPostsByUserId(_user!.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Error loading posts: ${snapshot.error}'),
          );
        }

        final posts = snapshot.data ?? [];

        if (posts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.grid_on,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No posts yet',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(
                AppConstants.paddingSmall,
                AppConstants.paddingSmall,
                AppConstants.paddingSmall,
                100, // Bottom padding for navigation gap
              ),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final post = posts[index];
                    return PostCard(
                      post: post,
                      showUserInfo: true, // Show user info like in feed
                    );
                  },
                  childCount: posts.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatJoiningDate(DateTime joinDate) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[joinDate.month - 1]} ${joinDate.year}';
  }

  Future<void> _toggleFollow() async {
    if (_user == null) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to follow users'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    try {
      await UserService.toggleFollow(
        currentUserId: currentUser.id,
        targetUserId: _user!.id,
      );

      // Reload user profile to get updated follower count and status
      await _loadUserProfile();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isFollowing ? 'Following ${_user!.displayName}' : 'Unfollowed ${_user!.displayName}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _startChat() async {
    if (_user == null) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to start a chat'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    if (currentUser.id == _user!.id) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You cannot chat with yourself'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
        ),
      ),
    );

    try {
      // Create or get existing chat
      final chat = await ChatService.createOrGetChat(
        currentUserId: currentUser.id,
        otherUserId: _user!.id,
        currentUserName: currentUser.displayName,
        otherUserName: _user!.displayName,
      );

      // Hide loading indicator
      Navigator.of(context).pop();

      if (chat != null) {
        // Navigate to chat conversation
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatConversationScreen(
              chat: chat,
              otherUser: _user!,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start chat. Please try again.'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      // Hide loading indicator
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting chat: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_link':
        _copyProfileLink();
        break;
      case 'block':
        _blockUser();
        break;
      case 'report':
        _reportUser();
        break;
    }
  }

  void _copyProfileLink() async {
    if (_user == null) return;

    // Create profile link
    final profileLink = 'https://amalpoint.com/profile/${_user!.id}';

    try {
      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: profileLink));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile link copied to clipboard'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to copy link'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _blockUser() {
    if (_user == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppConstants.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.block,
                  color: AppConstants.errorColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text('Block User'),
            ],
          ),
          content: Text(
            'Are you sure you want to block ${_user!.displayName}? You won\'t see their posts or be able to message them.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                backgroundColor: AppConstants.backgroundColor,
                foregroundColor: AppConstants.textSecondaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Cancel',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(width: 8),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual blocking functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${_user!.displayName} has been blocked'),
                    backgroundColor: AppConstants.errorColor,
                  ),
                );
              },
              style: TextButton.styleFrom(
                backgroundColor: AppConstants.errorColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Block',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _reportUser() {
    if (_user == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppConstants.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.report,
                  color: AppConstants.warningColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text('Report User'),
            ],
          ),
          content: Text(
            'Report ${_user!.displayName} for inappropriate behavior? Our team will review this report.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                backgroundColor: AppConstants.backgroundColor,
                foregroundColor: AppConstants.textSecondaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Cancel',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(width: 8),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual reporting functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${_user!.displayName} has been reported'),
                    backgroundColor: AppConstants.warningColor,
                  ),
                );
              },
              style: TextButton.styleFrom(
                backgroundColor: AppConstants.warningColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Report',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
