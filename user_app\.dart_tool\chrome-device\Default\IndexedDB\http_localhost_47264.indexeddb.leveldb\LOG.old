2025/07/26-22:42:49.364 2620 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.eabc4faa\flutter_tools_chrome_device.30261a8d\Default\IndexedDB\http_localhost_47264.indexeddb.leveldb/MANIFEST-000001
2025/07/26-22:42:49.364 2620 Recovering log #4
2025/07/26-22:42:49.365 2620 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.eabc4faa\flutter_tools_chrome_device.30261a8d\Default\IndexedDB\http_localhost_47264.indexeddb.leveldb/000004.log 
2025/07/26-22:42:49.378 2620 Level-0 table #8: started
2025/07/26-22:42:49.381 2620 Level-0 table #8: 6575 bytes OK
2025/07/26-22:42:49.384 2620 Delete type=0 #4
2025/07/26-22:42:49.384 5e6c Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/26-22:44:07.789 4e58 Compacting 1@1 + 1@2 files
2025/07/26-22:44:07.792 4e58 Generated table #9@1: 41 keys, 3060 bytes
2025/07/26-22:44:07.792 4e58 Compacted 1@1 + 1@2 files => 3060 bytes
2025/07/26-22:44:07.794 4e58 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/26-22:44:07.795 4e58 Delete type=2 #5
2025/07/26-22:44:07.795 4e58 Delete type=2 #8
2025/07/26-22:54:56.152 4e58 Level-0 table #11: started
2025/07/26-22:54:56.154 4e58 Level-0 table #11: 6034 bytes OK
2025/07/26-22:54:56.157 4e58 Delete type=0 #7
2025/07/26-22:54:56.157 4e58 Manual compaction at level-0 from (begin) .. (end); will stop at (end)
2025/07/26-22:54:56.158 4e58 Manual compaction at level-1 from (begin) .. (end); will stop at '\x00\x02\x01\x02\x01C\x00f\x00i\x00r\x00e\x00b\x00a\x00s\x00e\x00:\x00a\x00u\x00t\x00h\x00U\x00s\x00e\x00r\x00:\x00A\x00I\x00z\x00a\x00S\x00y\x00A\x00u\x00H\x00Y\x008\x00Y\x00g\x00y\x00h\x00M\x00z\x00p\x001\x008\x003\x00K\x00W\x00C\x007\x000\x00o\x00K\x00i\x00J\x00_\x00f\x00K\x00Y\x00M\x00Y\x00b\x00A\x00A\x00:\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]' @ 206 : 1
2025/07/26-22:54:56.158 4e58 Compacting 1@1 + 1@2 files
2025/07/26-22:54:56.160 4e58 Generated table #12@1: 36 keys, 2877 bytes
2025/07/26-22:54:56.160 4e58 Compacted 1@1 + 1@2 files => 2877 bytes
2025/07/26-22:54:56.161 4e58 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/26-22:54:56.162 4e58 Delete type=2 #9
2025/07/26-22:54:56.162 4e58 Delete type=2 #11
2025/07/26-22:54:56.163 4e58 Manual compaction at level-1 from '\x00\x02\x01\x02\x01C\x00f\x00i\x00r\x00e\x00b\x00a\x00s\x00e\x00:\x00a\x00u\x00t\x00h\x00U\x00s\x00e\x00r\x00:\x00A\x00I\x00z\x00a\x00S\x00y\x00A\x00u\x00H\x00Y\x008\x00Y\x00g\x00y\x00h\x00M\x00z\x00p\x001\x008\x003\x00K\x00W\x00C\x007\x000\x00o\x00K\x00i\x00J\x00_\x00f\x00K\x00Y\x00M\x00Y\x00b\x00A\x00A\x00:\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]' @ 206 : 1 .. (end); will stop at (end)
