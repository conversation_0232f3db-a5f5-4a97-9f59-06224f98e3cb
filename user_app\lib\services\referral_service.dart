import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';
import '../models/referral_model.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';

class ReferralService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'referrals';
  static const String _settingsCollection = 'referral_settings';

  /// Generate unique referral code
  static String generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return List.generate(8, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Create a new referral
  static Future<ReferralModel?> createReferral({
    required String referrerId,
    required String refereeId,
    required String referrerName,
    required String referrerEmail,
    required String refereeName,
    required String refereeEmail,
  }) async {
    try {
      // Check if referral already exists
      final existingReferral = await _firestore
          .collection(_collection)
          .where('referrerId', isEqualTo: referrerId)
          .where('refereeId', isEqualTo: refereeId)
          .get();

      if (existingReferral.docs.isNotEmpty) {
        throw Exception('Referral already exists between these users');
      }

      // Get referral settings
      final settings = await getReferralSettings();
      final rewardAmount = settings['rewardAmount'] ?? 50.0;
      final expiryDays = settings['expiryDays'] ?? 30;

      final referral = ReferralModel(
        id: _firestore.collection(_collection).doc().id,
        referrerId: referrerId,
        refereeId: refereeId,
        referralCode: generateReferralCode(),
        status: ReferralStatus.completed,
        rewardAmount: rewardAmount,
        rewardType: 'cash',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        completedAt: DateTime.now(),
        expiresAt: DateTime.now().add(Duration(days: expiryDays)),
        referrerName: referrerName,
        referrerEmail: referrerEmail,
        refereeName: refereeName,
        refereeEmail: refereeEmail,
      );

      await _firestore
          .collection(_collection)
          .doc(referral.id)
          .set(referral.toMap());

      return referral;
    } catch (e) {
      print('Error creating referral: $e');
      return null;
    }
  }

  /// Get referrals by user ID (as referrer)
  static Future<List<ReferralModel>> getReferralsByReferrer(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('referrerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReferralModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching referrals by referrer: $e');
      return [];
    }
  }

  /// Get referrals by user ID (as referee)
  static Future<List<ReferralModel>> getReferralsByReferee(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('refereeId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReferralModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching referrals by referee: $e');
      return [];
    }
  }

  /// Complete a referral
  static Future<bool> completeReferral(String referralId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(referralId)
          .update({
        'status': ReferralStatus.completed.value,
        'completedAt': Timestamp.fromDate(DateTime.now()),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error completing referral: $e');
      return false;
    }
  }

  /// Get referral statistics for a user
  static Future<Map<String, dynamic>> getReferralStats(String userId) async {
    try {
      final referrals = await getReferralsByReferrer(userId);

      final totalReferrals = referrals.length;

      return {
        'totalReferrals': totalReferrals,
      };
    } catch (e) {
      print('Error getting referral stats: $e');
      return {
        'totalReferrals': 0,
      };
    }
  }

  /// Get referral settings
  static Future<Map<String, dynamic>> getReferralSettings() async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc('default')
          .get();

      if (doc.exists) {
        return doc.data() ?? {};
      } else {
        // Return default settings
        return {
          'rewardAmount': 50.0,
          'expiryDays': 30,
          'isEnabled': true,
          'minimumPurchaseAmount': 100.0,
        };
      }
    } catch (e) {
      print('Error getting referral settings: $e');
      return {
        'rewardAmount': 50.0,
        'expiryDays': 30,
        'isEnabled': true,
        'minimumPurchaseAmount': 100.0,
      };
    }
  }

  /// Update referral settings (admin only)
  static Future<bool> updateReferralSettings(Map<String, dynamic> settings) async {
    try {
      await _firestore
          .collection(_settingsCollection)
          .doc('default')
          .set({
        ...settings,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      }, SetOptions(merge: true));

      return true;
    } catch (e) {
      print('Error updating referral settings: $e');
      return false;
    }
  }

  /// Get user's referral code
  static Future<String?> getUserReferralCode(String userId) async {
    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        return userData?['referralCode'];
      }
      return null;
    } catch (e) {
      print('Error getting user referral code: $e');
      return null;
    }
  }

  /// Set user's referral code
  static Future<bool> setUserReferralCode(String userId, String referralCode) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'referralCode': referralCode,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error setting user referral code: $e');
      return false;
    }
  }

  /// Find user by referral code
  static Future<UserModel?> findUserByReferralCode(String referralCode) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return UserModel.fromDocument(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Error finding user by referral code: $e');
      return null;
    }
  }

  /// Get all referrals (admin only)
  static Future<List<ReferralModel>> getAllReferrals({
    ReferralStatus? statusFilter,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      if (statusFilter != null) {
        query = query.where('status', isEqualTo: statusFilter.value);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => ReferralModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting all referrals: $e');
      return [];
    }
  }

  /// Expire old referrals - No longer needed since referrals auto-complete
  static Future<void> expireOldReferrals() async {
    // No longer needed since referrals are auto-completed
    print('Referrals are now auto-completed, no expiration needed');
  }
}
