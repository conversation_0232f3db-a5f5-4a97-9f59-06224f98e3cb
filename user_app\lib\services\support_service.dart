import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/support_model.dart';

class SupportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'support_requests';

  /// Submit a support request
  static Future<bool> submitSupportRequest({
    String? referenceId,
    required String contactNumber,
    required String address,
    required String category,
    required String message,
  }) async {
    try {
      final docRef = _firestore.collection(_collection).doc();
      final now = DateTime.now();

      final supportRequest = SupportModel(
        id: docRef.id,
        referenceId: referenceId,
        contactNumber: contactNumber,
        address: address,
        category: category,
        message: message,
        status: SupportStatus.pending,
        createdAt: now,
        updatedAt: now,
      );

      await docRef.set(supportRequest.toMap());
      return true;
    } catch (e) {
      print('Error submitting support request: $e');
      return false;
    }
  }

  /// Get user's support requests
  static Future<List<SupportModel>> getUserSupportRequests({
    String? referenceId,
    String? contactNumber,
    int limit = 20,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      if (referenceId != null && referenceId.isNotEmpty) {
        query = query.where('referenceId', isEqualTo: referenceId);
      } else if (contactNumber != null && contactNumber.isNotEmpty) {
        query = query.where('contactNumber', isEqualTo: contactNumber);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => SupportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting user support requests: $e');
      return [];
    }
  }

  /// Get support request by ID
  static Future<SupportModel?> getSupportRequestById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return SupportModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting support request: $e');
      return null;
    }
  }
}
