2025/07/28-10:14:49.920 5350 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.bb415e0e\flutter_tools_chrome_device.21a22e8c\Default\IndexedDB\http_localhost_16102.indexeddb.leveldb/MANIFEST-000001
2025/07/28-10:14:49.921 5350 Recovering log #10
2025/07/28-10:14:49.921 5350 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.bb415e0e\flutter_tools_chrome_device.21a22e8c\Default\IndexedDB\http_localhost_16102.indexeddb.leveldb/000010.log 
2025/07/28-10:14:49.942 16f0 Level-0 table #15: started
2025/07/28-10:14:49.944 16f0 Level-0 table #15: 849 bytes OK
2025/07/28-10:14:49.946 16f0 Delete type=0 #10
2025/07/28-10:14:49.946 16f0 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/28-10:16:07.545 60bc Compacting 1@1 + 1@2 files
2025/07/28-10:16:07.554 60bc Generated table #16@1: 44 keys, 3018 bytes
2025/07/28-10:16:07.554 60bc Compacted 1@1 + 1@2 files => 3018 bytes
2025/07/28-10:16:07.556 60bc compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/28-10:16:07.557 60bc Delete type=2 #12
2025/07/28-10:16:07.557 60bc Delete type=2 #15
