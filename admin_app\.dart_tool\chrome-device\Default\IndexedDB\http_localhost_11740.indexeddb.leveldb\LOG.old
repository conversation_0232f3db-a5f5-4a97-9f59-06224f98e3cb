2025/07/28-08:35:37.034 224c Creating DB C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d2388e47\flutter_tools_chrome_device.5b8c9e4e\Default\IndexedDB\http_localhost_11740.indexeddb.leveldb since it was missing.
2025/07/28-08:35:37.044 224c Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d2388e47\flutter_tools_chrome_device.5b8c9e4e\Default\IndexedDB\http_localhost_11740.indexeddb.leveldb/MANIFEST-000001
2025/07/28-08:35:37.071 3254 Level-0 table #5: started
2025/07/28-08:35:37.075 3254 Level-0 table #5: 1311 bytes OK
2025/07/28-08:35:37.079 3254 Delete type=0 #3
2025/07/28-08:35:37.081 11ec Manual compaction at level-0 from '\x00\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/28-09:26:12.410 11ec Level-0 table #7: started
2025/07/28-09:26:12.412 11ec Level-0 table #7: 4283 bytes OK
2025/07/28-09:26:12.416 11ec Delete type=0 #4
2025/07/28-09:26:12.416 11ec Manual compaction at level-0 from (begin) .. (end); will stop at (end)
2025/07/28-09:26:12.417 11ec Manual compaction at level-1 from (begin) .. (end); will stop at '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x005\x003\x001\x009\x007\x004\x002\x009\x008\x001\x008\x009\x00:\x00w\x00e\x00b\x00:\x006\x004\x00c\x002\x003\x003\x000\x003\x009\x00f\x001\x003\x00e\x002\x007\x00a\x00b\x008\x00d\x00a\x000\x004' @ 122 : 1
2025/07/28-09:26:12.417 11ec Compacting 1@1 + 1@2 files
2025/07/28-09:26:12.420 11ec Generated table #8@1: 35 keys, 2864 bytes
2025/07/28-09:26:12.420 11ec Compacted 1@1 + 1@2 files => 2864 bytes
2025/07/28-09:26:12.423 11ec compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/28-09:26:12.424 11ec Delete type=2 #5
2025/07/28-09:26:12.424 11ec Delete type=2 #7
2025/07/28-09:26:12.425 11ec Manual compaction at level-1 from '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x005\x003\x001\x009\x007\x004\x002\x009\x008\x001\x008\x009\x00:\x00w\x00e\x00b\x00:\x006\x004\x00c\x002\x003\x003\x000\x003\x009\x00f\x001\x003\x00e\x002\x007\x00a\x00b\x008\x00d\x00a\x000\x004' @ 122 : 1 .. (end); will stop at (end)
